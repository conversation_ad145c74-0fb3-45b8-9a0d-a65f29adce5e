#!/usr/bin/env python3
"""
Demo script to show the full-screen TUI in action
"""
import time
import asyncio
from ipcrawler.loading import ScanUI

async def demo_tui():
    """Demonstrate the full-screen TUI with simulated scans"""
    print("🚀 Starting TUI Demo...")
    print("⏳ Initializing... (3 seconds)")
    await asyncio.sleep(3)
    
    # Create and start the TUI
    ui = ScanUI("demo.com")
    ui.set_start_time(time.time())
    ui.start_ui()
    
    # Simulate some scanning activity
    await asyncio.sleep(2)
    
    # Start some tools
    ui.start_tool("Port Scan", "top-1000-ports")
    await asyncio.sleep(3)
    
    ui.start_tool("Directory Buster", "common.txt")
    await asyncio.sleep(2)
    
    ui.start_tool("Nikto Scan")
    await asyncio.sleep(3)
    
    # Complete some tools
    ui.complete_tool("Port Scan", "Found 3 open ports")
    await asyncio.sleep(2)
    
    ui.start_tool("SSH Enumeration")
    await asyncio.sleep(3)
    
    ui.complete_tool("Directory Buster", "Found 12 directories")
    await asyncio.sleep(2)
    
    ui.error_tool("Nikto Scan", "Connection timeout")
    await asyncio.sleep(3)
    
    ui.complete_tool("SSH Enumeration", "Banner grabbed")
    await asyncio.sleep(2)
    
    # Stop the TUI
    ui.stop_ui()
    print("\n✅ Demo completed!")

if __name__ == "__main__":
    asyncio.run(demo_tui())