import time
from datetime import datetime
from typing import Dict, Optional
from dataclasses import dataclass
from enum import Enum

from rich.console import Console
from rich.live import Live
from rich.panel import Panel
from rich.text import Text
from rich import box
from rich.layout import Layout
from rich.table import Table
from rich.spinner import Spinner
from rich.columns import Columns
from rich.align import Align
from ipcrawler.config import config

console = Console()

class ToolStatus(Enum):
    PENDING = "pending"
    RUNNING = "running" 
    COMPLETED = "completed"
    ERROR = "error"

@dataclass
class ToolInfo:
    name: str
    status: ToolStatus = ToolStatus.PENDING
    summary: Optional[str] = None
    start_time: Optional[float] = None
    end_time: Optional[float] = None

class ScanUI:
    """Full-screen TUI application for IPCrawler scans"""
    
    def __init__(self, target: str):
        self.target = target
        self.scan_started = False
        self.scan_start_time = None
        self.tools: Dict[str, ToolInfo] = {}
        self.console = Console()
        self.live = None
        self._is_active = False
        self.layout = None
        self.running_tools = {}
        self.completed_tools = {}
        
    def set_start_time(self, start_time: float):
        """Update the scan start time and mark as started"""
        self.scan_start_time = start_time
        self.scan_started = True
        
    def add_tool(self, tool_name: str):
        """Register a new tool that will be run"""
        self.tools[tool_name] = ToolInfo(name=tool_name)
        self._update_display()
        
    def start_tool(self, tool_name: str, wordlist_info: Optional[str] = None):
        """Mark a tool as started/running"""
        display_name = tool_name
        if wordlist_info:
            display_name = f"{tool_name} using {wordlist_info}"
            
        tool_info = ToolInfo(
            name=display_name, 
            status=ToolStatus.RUNNING,
            start_time=time.time()
        )
        
        self.tools[tool_name] = tool_info
        self.running_tools[tool_name] = tool_info
        # Remove from completed if it was there
        self.completed_tools.pop(tool_name, None)
        self._update_display()
            
    def complete_tool(self, tool_name: str, summary: Optional[str] = None):
        """Mark a tool as completed with optional summary"""
        if tool_name in self.tools:
            self.tools[tool_name].status = ToolStatus.COMPLETED
            self.tools[tool_name].summary = summary
            self.tools[tool_name].end_time = time.time()
            
            # Move from running to completed
            self.completed_tools[tool_name] = self.tools[tool_name]
            self.running_tools.pop(tool_name, None)
            self._update_display()
            
    def error_tool(self, tool_name: str, error_msg: Optional[str] = None):
        """Mark a tool as errored"""
        if tool_name in self.tools:
            self.tools[tool_name].status = ToolStatus.ERROR
            self.tools[tool_name].summary = error_msg or "Error"
            self.tools[tool_name].end_time = time.time()
            
            # Move from running to completed
            self.completed_tools[tool_name] = self.tools[tool_name]
            self.running_tools.pop(tool_name, None)
            self._update_display()
    
    def _format_elapsed_time(self) -> str:
        """Format elapsed time as HH:MM:SS"""
        if not self.scan_started or not self.scan_start_time:
            return "00:00:00"
            
        elapsed = int(time.time() - self.scan_start_time)
        hours = elapsed // 3600
        minutes = (elapsed % 3600) // 60
        seconds = elapsed % 60
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    
    def _get_status_icon(self, status: ToolStatus) -> str:
        """Get the icon for tool status"""
        icons = {
            ToolStatus.PENDING: "[ ]",
            ToolStatus.RUNNING: "[→]", 
            ToolStatus.COMPLETED: "[✓]",
            ToolStatus.ERROR: "[!]"
        }
        return icons[status]
    
    def _get_status_color(self, status: ToolStatus) -> str:
        """Get the color for tool status"""
        colors = {
            ToolStatus.PENDING: "dim white",
            ToolStatus.RUNNING: "yellow",
            ToolStatus.COMPLETED: "green", 
            ToolStatus.ERROR: "red"
        }
        return colors[status]
        
    def _create_header(self) -> Panel:
        """Create the header panel"""
        elapsed_time = self._format_elapsed_time()
        
        header_content = Table.grid(padding=1)
        header_content.add_column(justify="left")
        header_content.add_column(justify="center")
        header_content.add_column(justify="right")
        
        # Status indicator with spinner
        if self.running_tools:
            status_text = Text()
            status_text.append("●", style="bold green")
            status_text.append(" SCANNING", style="bold white")
        else:
            status_text = Text()
            status_text.append("○", style="dim white")
            status_text.append(" WAITING", style="dim white")
        
        header_content.add_row(
            f"Target: [bold cyan]{self.target}[/bold cyan]",
            "[bold white]IPCrawler Security Scanner[/bold white]",
            f"[bold white]Time: {elapsed_time}[/bold white]"
        )
        
        header_content.add_row(
            status_text,
            "",
            f"[dim white]Running: {len(self.running_tools)} | Completed: {len(self.completed_tools)}[/dim white]"
        )
        
        return Panel(
            header_content,
            title="[bold cyan]● IPCrawler TUI[/bold cyan]",
            border_style="cyan",
            box=box.HEAVY
        )
    
    def _create_running_panel(self) -> Panel:
        """Create the running tools panel"""
        if not self.running_tools:
            content = Align.center(
                Text("No active scans\nWaiting for tools to start...", 
                     style="dim white", justify="center"),
                vertical="middle"
            )
        else:
            table = Table(show_header=False, box=None, padding=(0, 1))
            table.add_column("Status", width=3)
            table.add_column("Tool", style="white")
            
            for tool_name, tool_info in self.running_tools.items():
                # Add spinner for running tools
                spinner_text = Text()
                spinner_text.append("⟳", style="bold yellow")
                
                table.add_row(
                    spinner_text,
                    tool_info.name
                )
            content = table
        
        return Panel(
            content,
            title="[bold yellow]⟳ Active Scans[/bold yellow]",
            border_style="yellow",
            box=box.ROUNDED
        )
    
    def _create_completed_panel(self) -> Panel:
        """Create the completed tools panel"""
        if not self.completed_tools:
            content = Align.center(
                Text("No completed scans yet", 
                     style="dim white", justify="center"),
                vertical="middle"
            )
        else:
            table = Table(show_header=False, box=None, padding=(0, 1))
            table.add_column("Status", width=3)
            table.add_column("Tool", style="white")
            table.add_column("Duration", style="dim white", justify="right")
            
            for tool_name, tool_info in list(self.completed_tools.items())[-10:]:  # Show last 10
                if tool_info.status == ToolStatus.COMPLETED:
                    icon = "✓"
                    style = "bold green"
                elif tool_info.status == ToolStatus.ERROR:
                    icon = "✗"
                    style = "bold red"
                else:
                    icon = "?"
                    style = "dim white"
                
                # Calculate duration
                if tool_info.start_time and tool_info.end_time:
                    duration = int(tool_info.end_time - tool_info.start_time)
                    if duration < 60:
                        duration_str = f"{duration}s"
                    else:
                        minutes = duration // 60
                        seconds = duration % 60
                        duration_str = f"{minutes}m {seconds}s"
                else:
                    duration_str = "—"
                
                table.add_row(
                    Text(icon, style=style),
                    tool_info.name,
                    duration_str
                )
            content = table
        
        return Panel(
            content,
            title="[bold green]✓ Completed Scans[/bold green]",
            border_style="green", 
            box=box.ROUNDED
        )
    
    def _create_layout(self):
        """Create the full-screen TUI layout"""
        layout = Layout()
        
        # Split into header and body
        layout.split_column(
            Layout(name="header", size=6),
            Layout(name="body")
        )
        
        # Split body into left and right panels
        layout["body"].split_row(
            Layout(name="running"),
            Layout(name="completed")
        )
        
        # Populate the layout
        layout["header"].update(self._create_header())
        layout["running"].update(self._create_running_panel())
        layout["completed"].update(self._create_completed_panel())
        
        return layout
    
    def start_ui(self):
        """Start the full-screen TUI"""
        if not self._is_active:
            # Clear the screen for full TUI experience
            self.console.clear()
            
            # Create the layout
            self.layout = self._create_layout()
            
            # Start live display with full screen refresh
            self.live = Live(
                self.layout,
                console=self.console,
                refresh_per_second=4,  # Higher refresh rate for smooth updates
                auto_refresh=True,
                screen=True,  # Take over the entire screen
                transient=False
            )
            self.live.start()
            self._is_active = True
    
    def _update_display(self):
        """Update the full-screen TUI layout"""
        if self.live and self._is_active and self.layout:
            try:
                # Update each section of the layout
                self.layout["header"].update(self._create_header())
                self.layout["running"].update(self._create_running_panel())
                self.layout["completed"].update(self._create_completed_panel())
            except Exception:
                # If update fails, continue silently
                pass
            
    def stop_ui(self, show_completion: bool = True):
        """Stop the full-screen TUI"""
        if self.live and self._is_active:
            self.live.stop()
            self.live = None
            self._is_active = False
            self.layout = None
            
            if show_completion:
                # Clear screen and show completion message
                self.console.clear()
                elapsed_str = self._format_elapsed_time()
                completed_tools = len(self.completed_tools)
                error_tools = sum(1 for t in self.completed_tools.values() if t.status == ToolStatus.ERROR)
                
                console.print()
                console.print(Panel.fit(
                    Text("\n✅ Scan Completed\n\n", style="green bold", justify="center") +
                    Text(f"Target: {self.target}\n", style="cyan") +
                    Text(f"Duration: {elapsed_str}\n", style="white") +
                    Text(f"Tools completed: {completed_tools}\n", style="green") +
                    Text(f"Tools with errors: {error_tools}\n\n", style="red" if error_tools > 0 else "green") +
                    Text("🎉 Thank you for using ipcrawler!\n", style="cyan bold", justify="center") +
                    Text("Results are available in results/ folder\n", style="dim white", justify="center"),
                    title="🕷️ Success",
                    border_style="green",
                    box=box.ROUNDED
                ))

# Global scan UI instance
scan_ui = None

def start_scan_ui(target: str):
    """Start the scan UI for a target"""
    global scan_ui
    if scan_ui is None:
        scan_ui = ScanUI(target)
        scan_ui.start_ui()

def stop_scan_ui(show_completion: bool = True):
    """Stop the scan UI"""
    global scan_ui
    if scan_ui:
        scan_ui.stop_ui(show_completion)
        scan_ui = None

def start_tool_loading(tool_name: str, target: str, command: str = "", estimated_minutes: Optional[int] = None, wordlist_info: Optional[str] = None):
    """Start loading for a tool"""
    global scan_ui
    if scan_ui is None:
        start_scan_ui(target)
    if scan_ui:
        scan_ui.start_tool(tool_name, wordlist_info)

def stop_tool_loading(success: bool = True, final_message: str = "", silent: bool = False):
    """Stop loading for a tool"""
    global scan_ui
    # Tool completion is handled by complete_tool_scan or error_tool_scan
    pass

def complete_tool_scan(tool_name: str, summary: Optional[str] = None):
    """Mark a tool as completed"""
    global scan_ui
    if scan_ui:
        scan_ui.complete_tool(tool_name, summary)

def error_tool_scan(tool_name: str, error_msg: Optional[str] = None):
    """Mark a tool as errored"""
    global scan_ui
    if scan_ui:
        scan_ui.error_tool(tool_name, error_msg)

def record_tool_activity(activity_type: str = "output"):
    """Record tool activity - no longer needed with new UI"""
    pass

def update_tool_progress(percentage: Optional[int] = None, status: str = ""):
    """Update tool progress - no longer needed with new UI"""
    pass

class ScanStatus:
    """Clean status display for scan operations"""

    @staticmethod
    def show_scan_start(target: str, plugin_name: str, verbosity: int = 0):
        """Update TUI with scan start"""
        global scan_ui
        if scan_ui:
            scan_ui.start_tool(plugin_name)
        else:
            # Fallback: start UI if not started yet
            start_scan_ui(target)
            if scan_ui:
                scan_ui.start_tool(plugin_name)

    @staticmethod
    def show_scan_completion(target: str, plugin_name: str, duration: str, success: bool = True, verbosity: int = 0):
        """Update TUI with scan completion"""
        global scan_ui
        if scan_ui:
            if success:
                scan_ui.complete_tool(plugin_name, f"Completed in {duration}")
            else:
                scan_ui.error_tool(plugin_name, "Failed")
    
    @staticmethod
    def show_service_discovery(target: str, service_name: str, protocol: str, port: int, verbosity: int = 0):
        """Show discovered service in TUI"""
        global scan_ui
        if scan_ui:
            tool_name = f"Discovered: {service_name} on {protocol}/{port}"
            scan_ui.add_tool(tool_name)
            scan_ui.complete_tool(tool_name)

    @staticmethod
    def show_command_execution(target: str, plugin_name: str, command: str, verbosity: int = 0):
        """Silent command execution - no output to prevent TUI stuttering"""
        pass

    @staticmethod
    def show_scan_result(target: str, plugin_name: str, result: str, level: str = "info", verbosity: int = 0):
        """Silent scan results - no output to prevent TUI stuttering"""
        pass

    @staticmethod
    def show_pattern_match(target: str, plugin_name: str, pattern: str, match: str, verbosity: int = 0):
        """Silent pattern matches - no output to prevent TUI stuttering"""
        pass

    @staticmethod
    def show_command_output(target: str, plugin_name: str, line: str, verbosity: int = 0):
        """Silent command output - no output to prevent TUI stuttering"""
        pass

    @staticmethod
    def show_progress_summary(active_scans: list, verbosity: int = 0, preserve_nmap_timing: bool = True):
        """Silent progress summary - no output to prevent TUI stuttering"""
        pass

    @staticmethod
    def show_verbosity_guide():
        """Silent verbosity guide - no output to prevent TUI stuttering"""
        pass

# Global status instance
scan_status = ScanStatus()