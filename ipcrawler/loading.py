import time
from datetime import datetime
from typing import Dict, Optional
from dataclasses import dataclass
from enum import Enum

from rich.console import Console
from rich.live import Live
from rich.panel import Panel
from rich.text import Text
from rich import box
from rich.layout import Layout
from rich.table import Table
from rich.spinner import Spinner
from rich.columns import Columns
from rich.align import Align
from ipcrawler.config import config

console = Console()

class ToolStatus(Enum):
    PENDING = "pending"
    RUNNING = "running" 
    COMPLETED = "completed"
    ERROR = "error"

@dataclass
class ToolInfo:
    name: str
    status: ToolStatus = ToolStatus.PENDING
    summary: Optional[str] = None
    start_time: Optional[float] = None
    end_time: Optional[float] = None

class ScanUI:
    """Full-screen TUI application for IPCrawler scans"""
    
    def __init__(self, target: str):
        self.target = target
        self.scan_started = False
        self.scan_start_time = None
        self.tools: Dict[str, ToolInfo] = {}
        self.console = Console()
        self.live = None
        self._is_active = False
        self.layout = None
        self.running_tools = {}
        self.completed_tools = {}
        
    def set_start_time(self, start_time: float):
        """Update the scan start time and mark as started"""
        self.scan_start_time = start_time
        self.scan_started = True
        
    def add_tool(self, tool_name: str):
        """Register a new tool that will be run"""
        self.tools[tool_name] = ToolInfo(name=tool_name)
        self._update_display()
        
    def start_tool(self, tool_name: str, wordlist_info: Optional[str] = None):
        """Mark a tool as started/running"""
        display_name = tool_name
        if wordlist_info:
            display_name = f"{tool_name} using {wordlist_info}"
            
        tool_info = ToolInfo(
            name=display_name, 
            status=ToolStatus.RUNNING,
            start_time=time.time()
        )
        
        self.tools[tool_name] = tool_info
        self.running_tools[tool_name] = tool_info
        # Remove from completed if it was there
        self.completed_tools.pop(tool_name, None)
        self._update_display()
            
    def complete_tool(self, tool_name: str, summary: Optional[str] = None):
        """Mark a tool as completed with optional summary"""
        if tool_name in self.tools:
            self.tools[tool_name].status = ToolStatus.COMPLETED
            self.tools[tool_name].summary = summary
            self.tools[tool_name].end_time = time.time()
            
            # Move from running to completed
            self.completed_tools[tool_name] = self.tools[tool_name]
            self.running_tools.pop(tool_name, None)
            self._update_display()
            
    def error_tool(self, tool_name: str, error_msg: Optional[str] = None):
        """Mark a tool as errored"""
        if tool_name in self.tools:
            self.tools[tool_name].status = ToolStatus.ERROR
            self.tools[tool_name].summary = error_msg or "Error"
            self.tools[tool_name].end_time = time.time()
            
            # Move from running to completed
            self.completed_tools[tool_name] = self.tools[tool_name]
            self.running_tools.pop(tool_name, None)
            self._update_display()
    
    def _format_elapsed_time(self) -> str:
        """Format elapsed time as HH:MM:SS"""
        elapsed = int(time.time() - self.start_time)
        hours = elapsed // 3600
        minutes = (elapsed % 3600) // 60
        seconds = elapsed % 60
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    
    def _get_status_icon(self, status: ToolStatus) -> str:
        """Get the icon for tool status"""
        icons = {
            ToolStatus.PENDING: "[ ]",
            ToolStatus.RUNNING: "[→]", 
            ToolStatus.COMPLETED: "[✓]",
            ToolStatus.ERROR: "[!]"
        }
        return icons[status]
    
    def _get_status_color(self, status: ToolStatus) -> str:
        """Get the color for tool status"""
        colors = {
            ToolStatus.PENDING: "dim white",
            ToolStatus.RUNNING: "yellow",
            ToolStatus.COMPLETED: "green", 
            ToolStatus.ERROR: "red"
        }
        return colors[status]
        
    def _render_panel(self) -> Panel:
        """Render the current scan status panel - show only running tools"""
        # Create the tools table - only show running tools
        tools_content = []
        current_time = time.time()
        
        for tool_name, tool_info in self.tools.items():
            # Only show running tools or recently completed ones (within 3 seconds)
            if tool_info.status == ToolStatus.RUNNING:
                icon = self._get_status_icon(tool_info.status)
                color = self._get_status_color(tool_info.status)
                
                # Build the line text with just the tool name
                line = f"{icon} {tool_info.name}"
                tools_content.append(Text(line, style=color))
            elif (tool_info.status in [ToolStatus.COMPLETED, ToolStatus.ERROR] and 
                  tool_info.end_time and 
                  current_time - tool_info.end_time < 3):
                # Show completed/error tools for 3 seconds
                icon = self._get_status_icon(tool_info.status)
                color = self._get_status_color(tool_info.status)
                line = f"{icon} {tool_info.name}"
                if tool_info.summary:
                    line += f": {tool_info.summary}"
                tools_content.append(Text(line, style=color))
        
        # If no active tools, show waiting message
        if not tools_content:
            tools_content.append(Text("Waiting for tools to start...", style="dim white"))
            
        # Create the main content
        content_lines = [
            Text(f"Target: {self.target}", style="bold cyan"),
            Text(f"Scan Time: {self._format_elapsed_time()}", style="bold white"),
            Text("─" * 24, style="dim white"),
        ]
        
        # Add tool lines
        content_lines.extend(tools_content)
        
        # Combine all content
        content = Text("\n").join(content_lines)
        
        return Panel(
            content,
            title="[bold cyan]IPCrawler Scan[/bold cyan]",
            border_style="cyan",
            box=box.ROUNDED,
            padding=(0, 1)
        )
    
    def start_ui(self):
        """Start the live UI"""
        if not self._is_active:
            self.live = Live(
                self._render_panel(),  # Call method to get initial panel
                console=self.console,
                refresh_per_second=2,
                auto_refresh=True,  # Enable auto refresh
                transient=False  # Keep content after stopping
            )
            self.live.start()
            self._is_active = True
    
    def _update_display(self):
        """Update the UI display"""
        if self.live and self._is_active:
            try:
                self.live.update(self._render_panel())
            except Exception:
                # If update fails, try to restart the live display
                pass
            
    def stop_ui(self, show_completion: bool = True):
        """Stop the live UI"""
        if self.live and self._is_active:
            self.live.stop()
            self.live = None
            self._is_active = False
            
            if show_completion:
                # Show completion message
                elapsed_str = self._format_elapsed_time()
                completed_tools = sum(1 for t in self.tools.values() if t.status == ToolStatus.COMPLETED)
                error_tools = sum(1 for t in self.tools.values() if t.status == ToolStatus.ERROR)
                
                console.print()
                console.print(Panel.fit(
                    Text("\n✅ Scan Completed\n\n", style="green bold", justify="center") +
                    Text(f"Target: {self.target}\n", style="cyan") +
                    Text(f"Duration: {elapsed_str}\n", style="white") +
                    Text(f"Tools completed: {completed_tools}\n", style="green") +
                    Text(f"Tools with errors: {error_tools}\n\n", style="red" if error_tools > 0 else "green") +
                    Text("🎉 Thank you for using ipcrawler!\n", style="cyan bold", justify="center") +
                    Text("Results are available in results/ folder\n", style="dim white", justify="center"),
                    title="🕷️ Success",
                    border_style="green",
                    box=box.ROUNDED
                ))

# Global scan UI instance
scan_ui = None

def start_scan_ui(target: str):
    """Start the scan UI for a target"""
    global scan_ui
    if scan_ui is None:
        scan_ui = ScanUI(target)
        scan_ui.start_ui()

def stop_scan_ui(show_completion: bool = True):
    """Stop the scan UI"""
    global scan_ui
    if scan_ui:
        scan_ui.stop_ui(show_completion)
        scan_ui = None

def start_tool_loading(tool_name: str, target: str, command: str = "", estimated_minutes: Optional[int] = None, wordlist_info: Optional[str] = None):
    """Start loading for a tool"""
    global scan_ui
    if scan_ui is None:
        start_scan_ui(target)
    if scan_ui:
        scan_ui.start_tool(tool_name, wordlist_info)

def stop_tool_loading(success: bool = True, final_message: str = "", silent: bool = False):
    """Stop loading for a tool"""
    global scan_ui
    # Tool completion is handled by complete_tool_scan or error_tool_scan
    pass

def complete_tool_scan(tool_name: str, summary: Optional[str] = None):
    """Mark a tool as completed"""
    global scan_ui
    if scan_ui:
        scan_ui.complete_tool(tool_name, summary)

def error_tool_scan(tool_name: str, error_msg: Optional[str] = None):
    """Mark a tool as errored"""
    global scan_ui
    if scan_ui:
        scan_ui.error_tool(tool_name, error_msg)

def record_tool_activity(activity_type: str = "output"):
    """Record tool activity - no longer needed with new UI"""
    pass

def update_tool_progress(percentage: Optional[int] = None, status: str = ""):
    """Update tool progress - no longer needed with new UI"""
    pass

class ScanStatus:
    """Clean status display for scan operations"""

    @staticmethod
    def show_scan_start(target: str, plugin_name: str, verbosity: int = 0):
        """Update TUI with scan start"""
        global scan_ui
        if scan_ui:
            scan_ui.start_tool(plugin_name)
        else:
            # Fallback: start UI if not started yet
            start_scan_ui(target)
            if scan_ui:
                scan_ui.start_tool(plugin_name)

    @staticmethod
    def show_scan_completion(target: str, plugin_name: str, duration: str, success: bool = True, verbosity: int = 0):
        """Update TUI with scan completion"""
        global scan_ui
        if scan_ui:
            if success:
                scan_ui.complete_tool(plugin_name, f"Completed in {duration}")
            else:
                scan_ui.error_tool(plugin_name, "Failed")
    
    @staticmethod
    def show_service_discovery(target: str, service_name: str, protocol: str, port: int, verbosity: int = 0):
        """Show discovered service in TUI"""
        global scan_ui
        if scan_ui:
            tool_name = f"Discovered: {service_name} on {protocol}/{port}"
            scan_ui.add_tool(tool_name)
            scan_ui.complete_tool(tool_name)

    @staticmethod
    def show_command_execution(target: str, plugin_name: str, command: str, verbosity: int = 0):
        """Silent command execution - no output to prevent TUI stuttering"""
        pass

    @staticmethod
    def show_scan_result(target: str, plugin_name: str, result: str, level: str = "info", verbosity: int = 0):
        """Silent scan results - no output to prevent TUI stuttering"""
        pass

    @staticmethod
    def show_pattern_match(target: str, plugin_name: str, pattern: str, match: str, verbosity: int = 0):
        """Silent pattern matches - no output to prevent TUI stuttering"""
        pass

    @staticmethod
    def show_command_output(target: str, plugin_name: str, line: str, verbosity: int = 0):
        """Silent command output - no output to prevent TUI stuttering"""
        pass

    @staticmethod
    def show_progress_summary(active_scans: list, verbosity: int = 0, preserve_nmap_timing: bool = True):
        """Silent progress summary - no output to prevent TUI stuttering"""
        pass

    @staticmethod
    def show_verbosity_guide():
        """Silent verbosity guide - no output to prevent TUI stuttering"""
        pass

# Global status instance
scan_status = ScanStatus()